import { expect } from '@playwright/test';
import { q, test } from 'agentq_web_automation_test';
import { Storage } from '@google-cloud/storage';
import * as path from 'path';
import * as fs from 'fs';

// Initialize Google Cloud Storage
const storage = new Storage({
  // You can set credentials here or use environment variables
  // keyFilename: 'path/to/service-account-key.json', // Optional: if not using default credentials
  // projectId: 'your-project-id', // Optional: if not set in environment
});

// Function to download file from Google Cloud Storage
async function downloadFileFromGCS(gsUrl: string): Promise<string> {
  try {
    // Parse the gs:// URL
    const urlParts = gsUrl.replace('gs://', '').split('/');
    const bucketName = urlParts[0];
    const fileName = urlParts.slice(1).join('/');

    console.log(`📥 Downloading file from GCS: ${bucketName}/${fileName}`);

    // Create local downloads directory if it doesn't exist
    const downloadsDir = path.join(process.cwd(), 'downloads');
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }

    // Generate local file path
    const localFileName = path.basename(fileName);
    const localFilePath = path.join(downloadsDir, localFileName);

    // Download the file
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(fileName);

    await file.download({ destination: localFilePath });

    console.log(`✅ File downloaded successfully to: ${localFilePath}`);
    return localFilePath;

  } catch (error) {
    console.error(`❌ Failed to download file from GCS: ${error}`);
    throw error;
  }
}

// Load test data from environment
const testDataEnv = process.env.TEST_DATA;
const clientId = process.env.CLIENT_ID;
const testCaseId = process.env.TEST_CASE_ID;
let stepsData: any = null;

if (testDataEnv) {
  try {
    stepsData = JSON.parse(testDataEnv);
    // console.log(`Loaded test data for: ${stepsData.testCase.title}`);
  } catch (error) {
    console.error('Failed to parse test data:', error);
  }
}

// Use unique identifier instead of test title to prevent data mixing
const uniqueTestName = clientId ? `Test-${clientId}` : (testCaseId ? `TestCase-${testCaseId}` : 'Dynamic Test Case');

test(uniqueTestName, async ({ page }) => {
  // console.log('🚀 Starting native test execution');

  if (!stepsData || !stepsData.steps) {
    console.log('⚠️ No test data provided, running simple demo test');
    // Run a simple demo test
    await page.goto('https://example.com');
    await expect(page).toHaveTitle(/Example/);
    console.log('✅ Demo test completed successfully');
    return;
  }

  try {
    console.log(`📋 Executing ${stepsData.steps.length} test steps`);

    // Execute each step
    for (const step of stepsData.steps) {
      if (step.action === 'prompt' && step.value) {
        console.log(`Step: prompt ${step.value} ✓`);
        await q(step.value);
      } else if (step.action === 'Go to Page' && step.target) {
        console.log(`Step: goto ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
      } else if (step.action === 'goto' && step.target) {
        console.log(`Step: goto ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
      } else if (step.action === 'navigate' && step.target) {
        console.log(`Step: navigate ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
      } else if (step.action === 'Fill' && step.target && step.value) {
        console.log(`Step: fill ${step.target} ${step.value} ✓`);
        await page.fill(step.target, step.value);
      } else if (step.action === 'write' && step.target && step.value) {
        console.log(`Step: write ${step.target} ${step.value} ✓`);
        await page.fill(step.target, step.value);
      } else if (step.action === 'Click' && step.target) {
        console.log(`Step: click ${step.target} ✓`);
        await page.click(step.target);
      } else if (step.action === 'click' && step.target) {
        console.log(`Step: click ${step.target} ✓`);
        await page.click(step.target);
      } else if (step.action === 'Pause' && step.value) {
        console.log(`Step: pause ${step.value} ✓`);
        await page.waitForTimeout(parseInt(step.value) * 1000);
      } else if (step.action === 'pause' && step.value) {
        console.log(`Step: pause ${step.value} ✓`);
        await page.waitForTimeout(parseInt(step.value) * 1000);
      } else if (step.action === 'Upload' && step.target && step.value) {
        console.log(`Step: upload ${step.target} ${step.fileUrl} ✓`);
        let filePath = step.fileUrl;

        // If fileUrl is a Google Cloud Storage URL, download it first
        if (step.fileUrl && step.fileUrl.startsWith('gs://')) {
          filePath = await downloadFileFromGCS(step.fileUrl);
        }

        await page.setInputFiles(step.target, filePath);
      } else if (step.action === 'upload' && step.target && step.value) {
        console.log(`Step: upload ${step.target} ${step.fileUrl} ✓`);
        let filePath = step.fileUrl;

        // If fileUrl is a Google Cloud Storage URL, download it first
        if (step.fileUrl && step.fileUrl.startsWith('gs://')) {
          filePath = await downloadFileFromGCS(step.fileUrl);
        }

        await page.setInputFiles(step.target, filePath);
      } else if (step.action === 'assertText' && step.target && step.value) {
        console.log(`Step: assertText ${step.target} ${step.value} ✓`);
        await expect(page.locator(step.target)).toHaveText(step.value);
      } else if (step.action === 'assertUrl' && step.value) {
        console.log(`Step: assertUrl ${step.value} ✓`);
        await expect(page).toHaveURL(step.value);
      } else {
        console.log(`Step: ${step.action} - Skipped (unsupported action)`);
      }
    }

    console.log('✅ All test steps completed successfully');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
});
